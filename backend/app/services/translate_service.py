from typing import List

from app.llms import get_llm

class TranslateService:

    def __init__(self):
        pass

    def translate_language_code_list(
        self, language_code_list: List[str], target_language: str = "zh-CN"
    ) -> str:
        """
        翻译语言标识

        Args:
            language_code: 要翻译的语言代码，如 'en-US', 'zh-CN'
            target_language: 目标语言，默认为 'zh-CN'

        Returns:
            翻译后的语言名称
        """
        llm = get_llm(llm_type=LLMType)

        # if target_language not in self.language_names:
        #     return language_code  # 如果目标语言不支持，返回原始代码

        # language_map = self.language_names[target_language]
        # return language_map.get(language_code, language_code)

    def translate_content(self, key: str, target_language: str = "zh-CN") -> str:
        """
        翻译具体的内容key

        Args:
            key: 要翻译的内容key
            target_language: 目标语言，默认为 'zh-CN'

        Returns:
            翻译后的内容
        """
        if target_language not in self.content_translations:
            return key  # 如果目标语言不支持，返回原始key

        content_map = self.content_translations[target_language]
        return content_map.get(key, key)

    def get_supported_languages(self) -> list:
        """
        获取支持的语言列表

        Returns:
            支持的语言代码列表
        """
        return list(self.language_names.keys())

    def get_available_content_keys(self, language: str = "zh-CN") -> list:
        """
        获取指定语言的可用内容key列表

        Args:
            language: 语言代码

        Returns:
            可用的内容key列表
        """
        if language not in self.content_translations:
            return []
        return list(self.content_translations[language].keys())
